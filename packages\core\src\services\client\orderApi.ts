import {
  ADD_AFTER_SALE_TRACK,
  APPLY_ORDER_RETURN,
  CANCEL_ORDER,
  CA<PERSON><PERSON>_ORDER_RETURN,
  GET_AFTER_SALE_DETAIL,
  GET_AFTER_SALE_TRACK,
  GET_AFTER_SALES,
  GET_AVAILABLE_PAYMENTS,
  GET_ORDER_DETAIL,
  GET_ORDER_LOGISTICS,
  GET_ORDER_REQUISITIONS_LIST,
  GET_ORDER_STATUS,
  GET_ORDERS,
} from '../../graphql'
import {
  AddAfterSaleTrackMutation,
  AddAfterSaleTrackMutationVariables,
  ApplyOrderReturnMutation,
  ApplyOrderReturnMutationVariables,
  CancelOrderMutation,
  CancelOrderMutationVariables,
  CancelOrderReturnMutation,
  CancelOrderReturnMutationVariables,
  GetAfterSaleDetailQuery,
  GetAfterSaleDetailQueryVariables,
  GetAfterSalesQuery,
  GetAfterSalesQueryVariables,
  GetAfterSaleTrackQuery,
  GetAfterSaleTrackQueryVariables,
  GetAvailablePaymentsQuery,
  GetAvailablePaymentsQueryVariables,
  GetOrderDetailQuery,
  GetOrderDetailQueryVariables,
  GetOrderLogisticsQuery,
  GetOrderLogisticsQueryVariables,
  GetOrderRequisitionListQuery,
  GetOrderRequisitionListQueryVariables,
  GetOrdersQuery,
  GetOrdersQueryVariables,
  GetOrderStatusQuery,
  GetOrderStatusQueryVariables,
} from '../../graphql/generated/graphql'
import rootApi from '../../utils/rootApi'

/**
 * 订单相关 api
 */
const orderApi = rootApi.injectEndpoints({
  endpoints: (build) => ({
    /**
     * 获取订单列表
     */
    getOrders: build.query<GetOrdersQuery, GetOrdersQueryVariables>({
      query: (variables) => ({
        document: GET_ORDERS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 获取售后列表
     */
    getAfterSales: build.query<GetAfterSalesQuery, GetAfterSalesQueryVariables>({
      query: (variables) => ({
        document: GET_AFTER_SALES,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 获取订单状态
     */
    getOrderStatus: build.query<GetOrderStatusQuery, GetOrderStatusQueryVariables>({
      query: (variables) => ({
        document: GET_ORDER_STATUS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 获取订单详情
     */
    getOrderDetail: build.query<GetOrderDetailQuery, GetOrderDetailQueryVariables>({
      query: (variables) => ({
        document: GET_ORDER_DETAIL,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 获取售后详情
     */
    getAfterSaleDetail: build.query<GetAfterSaleDetailQuery, GetAfterSaleDetailQueryVariables>({
      query: (variables) => ({
        document: GET_AFTER_SALE_DETAIL,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 查询订单物流信息
     */
    getOrderLogistics: build.query<GetOrderLogisticsQuery, GetOrderLogisticsQueryVariables>({
      query: (variables) => ({
        document: GET_ORDER_LOGISTICS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 取消订单
     */
    cancelOrder: build.mutation<CancelOrderMutation, CancelOrderMutationVariables>({
      query: (variables) => ({
        document: CANCEL_ORDER,
        variables,
      }),
      extraOptions: { maxRetries: 0, isAuth: true },
    }),
    /**
     * 获取退货清单
     */
    getOrderRequisitionList: build.query<
      GetOrderRequisitionListQuery,
      GetOrderRequisitionListQueryVariables
    >({
      query: (variables) => ({
        document: GET_ORDER_REQUISITIONS_LIST,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 申请退货退款
     */
    applyOrderReturn: build.mutation<ApplyOrderReturnMutation, ApplyOrderReturnMutationVariables>({
      query: (variables) => ({
        document: APPLY_ORDER_RETURN,
        variables,
      }),
      extraOptions: { maxRetries: 0, isAuth: true },
    }),
    /**
     * 取消退货退款申请
     */
    cancelOrderReturn: build.mutation<
      CancelOrderReturnMutation,
      CancelOrderReturnMutationVariables
    >({
      query: (variables) => ({
        document: CANCEL_ORDER_RETURN,
        variables,
      }),
      extraOptions: { maxRetries: 0, isAuth: true },
    }),
    /**
     * 获取支付方式
     */
    getAvailablePayments: build.query<
      GetAvailablePaymentsQuery,
      GetAvailablePaymentsQueryVariables
    >({
      query: (variables) => ({
        document: GET_AVAILABLE_PAYMENTS,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 添加售后物流
     */
    addAfterSaleTrack: build.mutation<
      AddAfterSaleTrackMutation,
      AddAfterSaleTrackMutationVariables
    >({
      query: (variables) => ({
        document: ADD_AFTER_SALE_TRACK,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
    /**
     * 查询售后物流
     */
    getAfterSaleTrack: build.query<GetAfterSaleTrackQuery, GetAfterSaleTrackQueryVariables>({
      query: (variables) => ({
        document: GET_AFTER_SALE_TRACK,
        variables,
      }),
      extraOptions: { isAuth: true },
    }),
  }),
})

export const {
  useGetOrdersQuery,
  useLazyGetOrdersQuery,
  useLazyGetAfterSalesQuery,
  useGetOrderStatusQuery,
  useLazyGetOrderStatusQuery,
  useGetOrderDetailQuery,
  useGetAfterSaleDetailQuery,
  useLazyGetOrderLogisticsQuery,
  useCancelOrderMutation,
  useCancelOrderReturnMutation,
  useLazyGetOrderRequisitionListQuery,
  useApplyOrderReturnMutation,
  useLazyGetAvailablePaymentsQuery,
  useAddAfterSaleTrackMutation,
  useLazyGetAfterSaleTrackQuery,
} = orderApi

export default orderApi
