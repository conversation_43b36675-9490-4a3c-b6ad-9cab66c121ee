'use client'

import { TBaseComponentProps } from '@ninebot/core'
import type { GetCategoriesQuery } from '@ninebot/core/src/graphql/generated/graphql'

import { CategoryList } from './components.tsx'

type TCategories = NonNullable<GetCategoriesQuery['categories']>
export type TCategoryItems = NonNullable<TCategories['items']>
// 分类类型
export type TCategoryItem = NonNullable<TCategoryItems[number]>

export type TCategoryPageProps = TBaseComponentProps<{
  data: TCategoryItem
}>

/**
 * 分类页面
 * 该页面名称/banner区域需要SSR渲染，以下部分可CSR渲染
 */
const CategoryPage = ({ data }: TCategoryPageProps) => {
  const { uid } = data

  return (
    <div className="mb-48">
      <CategoryList uid={uid} />
    </div>
  )
}

export default CategoryPage
