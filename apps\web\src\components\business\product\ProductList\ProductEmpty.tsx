import { Empty } from 'antd'

import { Search } from '@/components'

export default function ProductEmpty({
  isSearch = false,
  searchTerm,
}: {
  isSearch?: boolean
  searchTerm?: string
}) {
  return (
    <div className="mb-48 mt-base-48 flex justify-center">
      <Empty
        image={<Search isSmall={false} />}
        imageStyle={{ height: 180 }}
        description={
          <div className="text-center">
            <div className="font-miSansDemiBold450 text-2xl text-[#0F0F0F]">
              {isSearch ? `未找到“${searchTerm}”相关的商品` : '抱歉，您查看的商品已下架'}
            </div>
            <div className="mt-base font-miSansRegular330 text-lg text-gray-3">
              {isSearch
                ? '您可以试着换个词再试试，或者通过类目来查找商品'
                : '您可以点击顶部搜索，重新输入关键词寻找相关商品'}
            </div>
          </div>
        }
      />
    </div>
  )
}
