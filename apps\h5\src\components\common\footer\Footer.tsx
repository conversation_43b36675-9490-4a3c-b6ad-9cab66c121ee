'use client'

import { useEffect, useMemo, useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useGetFooterNewsQuery, useGetSiteConfigQuery } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { FooterNewsCategory, SiteConfig } from '@ninebot/core/src/graphql/generated/graphql'
import { Collapse } from 'antd-mobile'

import { Link } from '@/i18n/navigation'

import { Close, Expand, NineBot } from './icons'

const Footer = () => {
  const pathName = usePathname()
  const { openPage } = useNavigate()
  const [footer, setFooter] = useState<FooterNewsCategory[]>([])
  const [footerSiteConfig, setFooterSiteConfig] = useState<SiteConfig>({})
  const { currentData } = useGetFooterNewsQuery({})
  const { currentData: siteConfig } = useGetSiteConfigQuery({})

  useEffect(() => {
    if (currentData?.home_footer_news && currentData?.home_footer_news?.length > 0) {
      setFooter(currentData?.home_footer_news as FooterNewsCategory[])
    }
  }, [currentData])

  useEffect(() => {
    if (siteConfig?.site_config) {
      setFooterSiteConfig(siteConfig?.site_config as SiteConfig)
    }
  }, [siteConfig])

  const legalLinks = useMemo(() => {
    const links: { name: string; url: string }[] = []
    if (footerSiteConfig.footer_links && Array.isArray(footerSiteConfig.footer_links)) {
      footerSiteConfig.footer_links.forEach((link) => {
        if (link?.label && link?.url) {
          links.push({
            name: link.label,
            url: link.url.url || '/' + link.url.value || '',
          })
        }
      })
    }
    return links
  }, [footerSiteConfig])

  // 判断是否为首页
  const isHomePage =
    pathName === '/' || /^\/[a-z]{2}$/.test(pathName) || /^\/[a-z]{2}\/$/.test(pathName)

  if (pathName.includes('/customer') || pathName === '/category') {
    return null
  }

  return (
    <footer
      className={`${isHomePage ? 'mt-[48px]' : ''} bg-gray-base px-8 pb-[64px] pt-[32px] text-font-secondary`}>
      <div className="max-container">
        {/* Logo */}
        <div className="mb-8 flex items-center justify-start">
          <Link href="/" className="transition-opacity hover:opacity-80">
            <NineBot />
          </Link>
        </div>

        {/* Main Footer Content using Collapse */}
        <Collapse className="footer-collapse mb-[32px] bg-transparent before:hidden">
          {footer.map((section, index) => (
            <Collapse.Panel
              style={{
                backgroundColor: '#f3f3f4',
                border: 'none',
              }}
              key={`${section.category_sort}-${index}`}
              title={
                <span className="font-miSansMedium380 text-[14px] leading-[17px] text-[#0F0F0F]">
                  {section.category_title}
                </span>
              }
              arrowIcon={(active) => (active ? <Close /> : <Expand />)}>
              <ul className="bg-[#f3f3f4]">
                {section?.contains_news?.map((item, index) => (
                  <li key={`${item?.news_sort}-${index}`} className="py-[14px]">
                    <button
                      className="font-miSansMedium380 text-[14px] leading-[17px] text-[#6E6E73]"
                      onClick={() => openPage({ ...item?.redirect })}>
                      {item?.news_title}
                    </button>
                  </li>
                ))}
              </ul>
            </Collapse.Panel>
          ))}
        </Collapse>

        {/* Social Media Icons */}
        <div className="mb-8 flex items-center justify-start gap-[16px]">
          {footerSiteConfig.footer_social && Array.isArray(footerSiteConfig.footer_social) && (
            <>
              {footerSiteConfig.footer_social.map((social) => {
                return (
                  <Link
                    key={social?.label}
                    href={social?.url?.url || `/${social?.url?.value}`}
                    className="hover:text-gray-900">
                    <Image
                      src={social?.logo || ''}
                      alt={social?.label || ''}
                      width={16}
                      height={16}
                    />
                  </Link>
                )
              })}
            </>
          )}
        </div>

        {/* Copyright */}
        <div className="mb-[16px] border-gray-200 text-left font-miSansMedium380 text-[12px] leading-[16px] text-[#6E6E73]">
          {footerSiteConfig.copyright_info || 'Copyright © Ninebot Inc. All rights reserved.'}
        </div>

        {/* Legal Links */}
        <div className="flex flex-wrap items-center justify-start gap-[16px]">
          {legalLinks.map((link, index) => (
            <div key={link.name} className="flex items-center">
              <Link
                href={link.url}
                className="font-miSansMedium380 text-[12px] leading-[16px] text-[#6E6E73]">
                {link.name}
              </Link>
              {index !== legalLinks.length - 1 && (
                <span className="ml-[16px] h-4 w-[1px] bg-gray-300" />
              )}
            </div>
          ))}
        </div>
      </div>
    </footer>
  )
}

export default Footer
