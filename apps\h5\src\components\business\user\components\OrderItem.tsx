'use client'

import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  mergeStyles,
  NCoinView,
  OrderDetailItem,
  OrderListItem,
  PaymentMethod,
  Price,
  ROUTE,
  sleep,
  useDebounceFn,
  useOrderDetail,
  useTimerCoundown,
  useUserOrder,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { TToastProps } from '@ninebot/core/src/components/toast/Toast'

import { CustomButton, PayMethodPopup } from '@/components'
import { commonStyles } from '@/constants'

import { MigrationProductItem } from './migrationProductItem'
import ProductItem from './ProductItem'

type OrderItemProps = {
  order: OrderListItem
  currentTime: number
  toast: {
    show: (params: TToastProps) => void
    hide: () => void
  }
  updateCancelOrderStatus: (number: string) => void
  handleRefresh: () => void
}

const OrderItem = ({
  order,
  currentTime,
  toast,
  updateCancelOrderStatus,
  handleRefresh,
}: OrderItemProps) => {
  const getI18nString = useTranslations('Common')
  const [now, setNow] = useState(currentTime)
  const { openPage } = useNavigate()

  // 当currentTime变化时更新now状态，确保倒计时基于最新时间计算
  useEffect(() => {
    setNow(currentTime)
  }, [currentTime])

  const [paymentVisible, setPaymentVisible] = useState(false)
  const [paymentsList, setPaymentsList] = useState<PaymentMethod[]>([])

  const { checkOrderPayInfo, getAvailablePayments } = useUserOrder()

  const onEnd = useCallback(() => {
    setTimeout(() => {
      updateCancelOrderStatus(order?.number)
    }, 200)
  }, [order?.number, updateCancelOrderStatus])

  const formattedRes = useTimerCoundown(
    now / 1000,
    order?.status_code === 'pending' ? Number(order?.payment_time_out || 0) : 0,
    1000,
    onEnd,
  )

  const {
    isMigrated,
    migrationOrder,
    migrationProductCount,
    migrationTotalNCoin,
    isReturn,
    hasNCoin,
    isOnlyNCoin,
    isWaitPay,
    isUnPaid,
    hasInvoice,
  } = useOrderDetail(order)

  /**
   * 拉下刷新时重置倒计时
   */
  useEffect(() => {
    if (currentTime) {
      setNow(currentTime)
    }
  }, [currentTime])

  /**
   * 监听当回到当前页面时，重置倒计时
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setNow(new Date().getTime())
      }
    }

    const handleFocus = () => {
      setNow(new Date().getTime())
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const formatTime = useCallback(
    (time: { total: number; days: number; hours: number; minutes: number; seconds: number }) => {
      const { minutes, seconds } = time

      const pad = (num: number) => String(num).padStart(2, '0')
      return [pad(minutes), pad(seconds)]
    },
    [],
  )

  const [minutes, seconds] = formatTime(formattedRes)

  /**
   * 跳转支付
   */
  const { run: handleRepay } = useDebounceFn(async () => {
    if (order?.encrypt?.nid && order?.total?.grand_total) {
      const result = await checkOrderPayInfo(order.encrypt.nid)
      // 202 跳转支付
      if (result?.code === 202) {
        const payments = await getAvailablePayments(order?.total?.grand_total?.value || 0)
        if (payments && payments?.length) {
          setPaymentVisible(true)
          setPaymentsList(payments as PaymentMethod[])
          return
        }

        return
      }
      // 200 已支付
      if (result?.code === 200) {
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: getI18nString('order_has_paid'),
        })
        await sleep(500)
        handleRefresh()
        return
      }
      // 支付失败
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: result?.message as string,
      })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 跳转订单详情
   */
  const { run: handleOrderDetail } = useDebounceFn(() => {
    if (order?.encrypt?.nid || order?.id) {
      if (isReturn) {
        openPage({
          route: ROUTE.accountOrderReturnDetail,
          queryParams: {
            returnId: order?.id || '',
          },
        })
      } else {
        openPage({
          route: ROUTE.accountOrderDetail,
          queryParams: {
            orderNumber: order?.encrypt?.nid || '',
          },
        })
      }
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 拉下刷新时重置倒计时
   */
  useEffect(() => {
    if (currentTime) {
      setNow(currentTime)
    }
  }, [currentTime])

  return (
    <>
      <div className="flex flex-col gap-8 rounded-[12px] bg-white px-[12px] py-[16px]">
        <div className="flex h-[2.6rem] items-center justify-between">
          <div className="font-miSansRegular330 text-[12px] leading-[14px] text-[#86868B]">
            {getI18nString('order_number')}
            {isMigrated
              ? migrationOrder?.order_num
              : isReturn
                ? order?.order_number
                : order?.number}
          </div>
          {isWaitPay ? (
            <div className="flex items-center justify-between overflow-hidden rounded-[8px] border border-[#FEE5E5]">
              <span className="px-[8px] py-[6px] text-[12px] leading-[14px] text-[#000000]">
                {getI18nString('wait_payment')}
              </span>
              <span className="w-[48px] bg-[#DA291C] py-[6px] text-center text-[12px] leading-[14px] text-[#FFFFFF]">
                {minutes}:{seconds}
              </span>
            </div>
          ) : (
            <div className="font-miSansMedium380 text-[16px] leading-[19px]">
              {isMigrated
                ? migrationOrder?.status
                : isReturn
                  ? order?.requisition_type_label
                  : order?.status_tab_label}
            </div>
          )}
        </div>
        {isMigrated
          ? migrationOrder?.items &&
            migrationOrder?.items?.length > 0 &&
            migrationOrder?.items.map((item, index) => {
              if (item) {
                return (
                  <div key={index} className="flex flex-row gap-[12px]">
                    <MigrationProductItem productInfo={item} showPrice={false} />
                  </div>
                )
              }
            })
          : order?.items &&
            order?.items?.length > 0 &&
            order?.items?.map((item) => (
              <div key={item?.id} className="flex flex-row gap-[12px]">
                <ProductItem
                  productInfo={item as OrderDetailItem}
                  showPrice={false}
                  showAttr={false}
                />
              </div>
            ))}
        <div className="flex h-[22px] items-center justify-end">
          <span className="mr-[10px] font-miSansMedium380 text-[12px] leading-[14px] text-[#86868B]">
            {getI18nString('total_products_count', {
              key: isMigrated
                ? migrationProductCount
                : isReturn
                  ? order?.total_qty
                  : order?.quantity_ordered,
            })}
          </span>
          <span className="mr-[4px] font-miSansMedium380 text-[14px] leading-[17px] text-[#0F0F0F]">
            {isReturn
              ? getI18nString('refund')
              : isUnPaid
                ? getI18nString('should_pay')
                : getI18nString('has_paid')}
          </span>
          {isOnlyNCoin ? (
            <NCoinView
              number={
                isMigrated
                  ? migrationTotalNCoin
                  : isReturn
                    ? order?.total?.refund_ncoin || 0
                    : order?.ncoin_pay?.grand_total
              }
              iconStyle={{ size: 18 }}
              textStyle={mergeStyles([commonStyles.font_18_bold, 'leading-[22px]'])}
              showZero
            />
          ) : hasNCoin ? (
            <div className={commonStyles.flex_row}>
              <Price
                price={isReturn ? order?.total?.refund_total : order?.total?.grand_total}
                currencyStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                fractionStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
              />
              {Number(isReturn ? order?.total?.refund_ncoin : order?.ncoin_pay?.grand_total) >
                0 && (
                <>
                  <div className="mx-[4px]">
                    <IconPlus />
                  </div>
                  <NCoinView
                    number={
                      isReturn ? order?.total?.refund_ncoin || 0 : order?.ncoin_pay?.grand_total
                    }
                    iconStyle={{ size: 18 }}
                    textStyle={mergeStyles([commonStyles.font_18_bold, 'leading-[22px]'])}
                  />
                </>
              )}
            </div>
          ) : (
            <Price
              price={isReturn ? order?.total?.refund_total : order?.total?.grand_total}
              color="primary"
              currencyStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
              textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
              fractionStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
            />
          )}
        </div>
        <div className={commonStyles.flex_row}>
          <div className="font-miSansRegular330 text-[12px] leading-[14px] text-[#86868B]">
            {isReturn ? order?.created_at.split(' ')[0] : order?.order_date.split(' ')[0]}
          </div>
          <div className={mergeStyles([commonStyles.flex_row, 'gap-[4px]'])}>
            {hasInvoice && (
              <CustomButton
                customStyle={{
                  width: 'auto',
                  height: '3.4rem',
                  marginLeft: 4,
                  padding: '0 1.6rem',
                  borderRadius: 20,
                  borderWidth: 1,
                  borderColor: '#BBBBBD',
                }}
                fill="outline"
                onClick={() => {
                  if (order?.invoice_info?.zlink) {
                    openPage({
                      route: ROUTE.webView,
                      webViewUrl: order.invoice_info.zlink,
                    })
                  }
                }}>
                <div className={commonStyles.font_14}>{getI18nString('view_invoice')}</div>
              </CustomButton>
            )}
            <CustomButton
              customStyle={{
                width: 'auto',
                height: '3.4rem',
                marginLeft: 4,
                padding: '0 1.6rem',
                borderRadius: 20,
                borderWidth: 1,
                borderColor: '#BBBBBD',
              }}
              fill="outline"
              onClick={handleOrderDetail}>
              <div className={commonStyles.font_14}>
                {isReturn ? getI18nString('view_detail') : getI18nString('order_detail')}
              </div>
            </CustomButton>
            {isWaitPay && (
              <CustomButton
                customStyle={{
                  width: 'auto',
                  height: '3.4rem',
                  marginLeft: 4,
                  padding: '0 1.2rem',
                  borderRadius: 20,
                  borderWidth: 1,
                  color: '#DA291C',
                  borderColor: '#DA291C',
                }}
                fill="outline"
                onClick={handleRepay}>
                <div className={mergeStyles([commonStyles.font_14, commonStyles.font_red])}>
                  {getI18nString('go_pay')}
                </div>
              </CustomButton>
            )}
          </div>
        </div>
      </div>

      <PayMethodPopup
        popupVisible={paymentVisible}
        tempOrderInfo={{
          orderId: order?.encrypt?.nid || '',
          totalAmount: order?.total?.grand_total,
          defaultPayment: order?.payment_methods?.[0]?.type || '',
        }}
        paymentsList={paymentsList}
        closePopup={() => {
          setPaymentVisible(false)
        }}
      />
    </>
  )
}

export default OrderItem
