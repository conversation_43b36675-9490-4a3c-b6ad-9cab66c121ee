import type { ThemeConfig } from 'antd'

const theme: ThemeConfig = {
  token: {
    // 主色
    colorPrimary: '#DA291C',
    // 品牌色的悬浮态背景
    colorPrimaryHover: '#DA291C',
    // 品牌色的深色激活态背景
    colorPrimaryActive: '#C40001',

    // 文字色
    colorText: '#000000', // font-primary
    colorTextSecondary: '#6E6E73', // font-secondary
    colorTextDisabled: '#00000020', // font-disable

    // 基础圆角
    borderRadius: 8,

    // 字体相关
    fontFamily: 'var(--font-family-miSansMedium380)',
  },
  components: {
    Button: {
      borderRadiusSM: 999,
      borderRadius: 999,
      borderRadiusLG: 999,
      controlHeightSM: 32,
      controlHeight: 44,
      controlHeightLG: 56,
      paddingInlineSM: 16,
      paddingInline: 24,
      paddingInlineLG: 32,
      paddingBlockSM: 8,
      paddingBlock: 12,
      paddingBlockLG: 16,
      contentLineHeightSM: 14 / 16,
      contentLineHeight: 16 / 20,
      contentLineHeightLG: 20 / 24,
      contentFontSizeSM: 14,
      contentFontSize: 16,
      contentFontSizeLG: 20,
      textTextColor: '#000000',
      defaultBg: '#F3F3F4',
      defaultBorderColor: '#F3F3F4',
      defaultActiveColor: '#000000',
      defaultActiveBg: '#E1E1E4',
      defaultActiveBorderColor: '#E1E1E4',
      defaultHoverColor: '#000000',
      defaultHoverBg: '#F8F8F9',
      defaultHoverBorderColor: '#F8F8F9',
      // 禁用态背景
      colorBgContainerDisabled: 'rgba(0, 0, 0, 0.08)',
      borderColorDisabled: 'transparent',
      defaultShadow: 'none',
      primaryShadow: 'none',
    },
    Carousel: {
      // 轮播图箭头样式
      dotWidth: 8,
      dotHeight: 8,
      dotActiveWidth: 24,
    },
    Message: {
      contentBg: '#000000A6',
      contentPadding: '12px 16px',
      colorText: '#FFFFFF',
      fontSizeLG: 16,
      lineHeightLG: 24,
      borderRadiusLG: 9999,
    },
    Menu: {
      itemHeight: 52,
      itemSelectedBg: '#F8F8F9',
      itemHoverBg: '#F8F8F9',
      itemSelectedColor: '#DA291C',
      itemBorderRadius: 70,
      itemMarginBlock: 12,
      iconMarginInlineEnd: 8,
      iconSize: 24,
    },
    Modal: {
      // Modal 组件样式配置
      paddingMD: 24,
      borderRadiusLG: 8,
      fontSizeHeading5: 16,
      colorBgContainer: '#FFFFFF',
      colorBgMask: 'rgba(0, 0, 0, 0.65)',
    },
    Table: {
      headerBg: '#FFFFFF',
      headerColor: '#222223',
      headerSplitColor: '#FFF',
      borderColor: '#fff',
      cellPaddingInline: 0,
      cellPaddingBlock: 24,
    },
    Breadcrumb: {
      separatorColor: '#000000',
      lastItemColor: '#000000',
      itemColor: '#444446',
      linkColor: '#444446',
    },
    Input: {
      activeBorderColor: '#0F0F0F',
      hoverBorderColor: '#DCDCDC',
      activeShadow: 'none',
      warningActiveShadow: 'none',
      errorActiveShadow: 'none',
      colorError: '#E34D59',
      colorErrorText: '#DA291C',
    },
    Form: {
      itemMarginBottom: 16,
    },
    Dropdown: {
      paddingBlock: 17,
      paddingXXS: 12,
      controlPaddingHorizontal: 24,
      borderRadiusLG: 12,
      borderRadiusSM: 999,
      controlItemBgHover: '#F8F8F9',
      fontSize: 16,
      lineHeight: 1.4,
    },
  },
}

export default theme
