'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  OrderListItem,
  setOrderFilterStatus,
  TRACK_EVENT,
  useLoadingContext,
  usePagination,
  userCancelOrderIdSelector,
  userOrderFilterStatusSelector,
  useUserOrder,
  useVolcAnalytics,
} from '@ninebot/core'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Pagination, Radio } from 'antd'
import clsx from 'clsx'

import { IconArrow } from '@/components'
import { Link } from '@/i18n/navigation'

import EmptyOrder from './EmptyOrder'
import { NextIcon, PrevIcon } from './icons'
import OrderItem from './OrderItem'
import OrderItemSkeleton from './OrderItemSkeleton'
const AFTER_SALES = 'order_requisition'

type TabItem = {
  label?: string | null
  status?: string | null
}

const OrderList = ({
  isPage = true,
  externalStatus,
}: {
  isPage?: boolean
  externalStatus?: string
}) => {
  const orderStatus = useAppSelector(userOrderFilterStatusSelector)
  const cancelOrderNumber = useAppSelector(userCancelOrderIdSelector)
  // 个人中心页面使用传入的 externalStatus，订单页面使用全局状态
  const [activeKey, setActiveKey] = useState(
    isPage ? externalStatus || orderStatus : externalStatus || '',
  )

  const loading = useLoadingContext()
  const getI18nString = useTranslations('Web')
  const dispatch = useAppDispatch()
  const { reportEvent } = useVolcAnalytics()

  const { page, pageSize, handlePageChange } = usePagination(1, isPage ? 10 : 4)

  const [ordersMap, setOrdersMap] = useState<
    Record<string, { orderList: OrderListItem[]; total: number }>
  >({})
  const [tabs, setTabs] = useState<TabItem[]>([])
  const [now, setNow] = useState(Date.now())
  const [dataLoaded, setDataLoaded] = useState(false)

  // 根据当前activeKey获取对应的订单数据
  const currentOrderData = useMemo(() => {
    return ordersMap[activeKey] || { orderList: [], total: 0 }
  }, [ordersMap, activeKey])

  const {
    getUserOrders,
    getOrderStatusInfo,
    getAfterSalesOrders,
    isGetOrderStatusLoading: orderStatusLoading,
    isGetOrdersLoading: orderFetching,
    isGetAfterSalesLoading: afterSalesFetching,
  } = useUserOrder()

  /**
   * 格式化 Tabs
   */
  const formatTabs = useCallback(
    (
      data: {
        label?: string | null
        status?: string | null
      }[],
    ) =>
      data.map((i) => ({
        label: i.label,
        status: i.status,
      })),
    [],
  )

  const getOrderStatus = useCallback(async () => {
    const orderStatusData = await getOrderStatusInfo()
    if (orderStatusData) {
      setTabs(formatTabs(orderStatusData as TabItem[]))
    }
  }, [getOrderStatusInfo, formatTabs])

  const getOrders = useCallback(
    async (activeKey: string) => {
      setDataLoaded(false)

      if (activeKey === AFTER_SALES) {
        const orderData = await getAfterSalesOrders(page, pageSize)
        if (orderData) {
          setOrdersMap((prev) => ({
            ...prev,
            [activeKey]: {
              orderList: orderData.orderList,
              total: orderData.total,
            },
          }))
        }
      } else {
        const orderData = await getUserOrders(page, pageSize, activeKey)
        if (orderData) {
          setOrdersMap((prev) => ({
            ...prev,
            [activeKey]: {
              orderList: orderData.orderList,
              total: orderData.total,
            },
          }))
        }
      }
      setDataLoaded(true)
    },
    [page, pageSize, getAfterSalesOrders, getUserOrders],
  )

  /**
   * 获取订单状态
   */
  useEffect(() => {
    if (isPage) {
      getOrderStatus()
    }
  }, [getOrderStatus, isPage])

  /**
   * 订单页面时同步全局状态变化
   */
  useEffect(() => {
    if (isPage && orderStatus !== activeKey) {
      setActiveKey(orderStatus)
    }
  }, [orderStatus, activeKey, isPage])

  /**
   * 请求订单列表
   */
  useEffect(() => {
    getOrders(activeKey)
  }, [getOrders, activeKey])

  /**
   * 切换订单筛选状态
   */
  const handleTabChange = useCallback(
    (key: string) => {
      // 只有在订单页面时才更新全局状态
      if (isPage) {
        dispatch(setOrderFilterStatus(key))
      }
      setActiveKey(key)
      // 更新当前时间，确保倒计时基于最新时间计算
      setNow(Date.now())
    },
    [dispatch, isPage],
  )

  /**
   * 页面初始 Loading
   */
  const isInitLoading = useMemo(() => orderStatusLoading, [orderStatusLoading])

  /**
   * 切换订单状态 Loading
   */
  const isFilterLoading = useMemo(
    () => page === 1 && !isInitLoading && (orderFetching || afterSalesFetching),
    [page, isInitLoading, orderFetching, afterSalesFetching],
  )

  /**
   * 分页加载状态
   */
  const isPaginationLoading = useMemo(
    () => page > 1 && (orderFetching || afterSalesFetching),
    [page, orderFetching, afterSalesFetching],
  )

  /**
   * 是否显示骨架屏
   */
  const showSkeleton = useMemo(
    () => isInitLoading || isFilterLoading || isPaginationLoading,
    [isInitLoading, isFilterLoading, isPaginationLoading],
  )

  /**
   * 修改订单状态为 '已取消'
   */
  const updateCancelOrderStatus = useCallback(
    (number: string) => {
      setOrdersMap((prev) => {
        const currentData = prev[activeKey]
        if (!currentData) return prev

        return {
          ...prev,
          [activeKey]: {
            ...currentData,
            orderList: currentData.orderList.map((item) =>
              item?.number === number
                ? {
                    ...item,
                    status_code: 'canceled',
                    status_tab_label: '取消',
                  }
                : item,
            ),
          },
        }
      })
    },
    [activeKey],
  )

  /**
   * 重置分页并拉取数据
   */
  const handleRefresh = async () => {
    if (page === 1) {
      await getOrders(activeKey)
    } else {
      handlePageChange(1)
    }
  }

  /**
   * 切换订单状态时重置分页
   */
  useEffect(() => {
    handlePageChange(1)
  }, [handlePageChange, activeKey])

  /**
   * 修改被取消订单状态
   */
  useEffect(() => {
    if (cancelOrderNumber) {
      setOrdersMap((prev) => {
        const currentData = prev[activeKey]
        if (!currentData) return prev

        return {
          ...prev,
          [activeKey]: {
            ...currentData,
            orderList: currentData.orderList.map((order) =>
              order?.number === cancelOrderNumber
                ? {
                    ...order,
                    status_code: 'canceled',
                    status_tab_label: getI18nString('cancelled'),
                  }
                : order,
            ),
          },
        }
      })
    }
  }, [cancelOrderNumber, getI18nString, activeKey])

  /**
   * 筛选订单 Loading
   */
  useEffect(() => {
    if (showSkeleton) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, showSkeleton])

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setNow(Date.now())
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  /**
   * 埋点：点击我的订单
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_order_page_exposure)
  }, [reportEvent])

  return (
    <div className="rounded-[20px] bg-white p-base-32">
      <div
        className={clsx(
          'flex border-b border-gray-base pb-base-32',
          isPage ? 'flex-col gap-base-32' : 'items-center justify-between',
        )}>
        <h3 className="font-miSansDemiBold450 text-[28px] leading-[1.2]">
          {getI18nString('order_list')}
        </h3>
        {!isPage && currentOrderData.orderList.length > 0 ? (
          <Link
            href="/customer/orders"
            className="flex h-8 flex-row items-center gap-[4px] transition-colors"
            onClick={() => {
              // 点击查看全部时，重置订单筛选状态为全部
              dispatch(setOrderFilterStatus(''))
            }}>
            <span className="font-miSansMedium380 text-[14px] leading-[20px] text-[#6E6E73]">
              {getI18nString('view_all')}
            </span>
            <IconArrow color="#6E6E73" rotate={-90} />
          </Link>
        ) : null}
        {/* 分类标签 */}
        {isPage && (
          <Radio.Group
            className="custom-radio-group nav-tabs"
            value={activeKey}
            onChange={(e) => handleTabChange(e.target.value)}>
            {tabs.map((tab) => (
              <Radio.Button key={tab.status} value={tab.status}>
                {tab.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        )}
      </div>

      {/* 加载状态下显示骨架屏 */}
      {showSkeleton && (
        <>
          <OrderItemSkeleton />
          <OrderItemSkeleton />
          <OrderItemSkeleton />
        </>
      )}

      {/* 订单数据为空且已完成加载且不在加载状态时显示空订单提示 */}
      {!currentOrderData.orderList.length && !showSkeleton && dataLoaded && <EmptyOrder />}

      {/* 使用分页后的订单数据进行渲染 - 只有在数据加载完成且不在加载状态时才显示 */}
      {currentOrderData.orderList.length > 0 &&
        !showSkeleton &&
        dataLoaded &&
        currentOrderData.orderList.map((order, index) => (
          <OrderItem
            order={order}
            key={order.id}
            currentTime={now}
            updateCancelOrderStatus={updateCancelOrderStatus}
            handleRefresh={handleRefresh}
            isLast={index === currentOrderData.orderList.length - 1}
          />
        ))}

      {isPage && currentOrderData.orderList.length > 0 && currentOrderData.total > pageSize && (
        <div className="mt-base-32 flex justify-center">
          <Pagination
            defaultCurrent={page}
            defaultPageSize={pageSize}
            current={page}
            pageSize={pageSize}
            total={currentOrderData.total}
            onChange={handlePageChange}
            className="custom-pagination"
            showQuickJumper={false}
            showSizeChanger={false}
            itemRender={(_current, type, originalElement) => {
              if (type === 'prev') {
                return (
                  <button className="flex h-base-32 w-base-32 items-center rounded-full bg-gray-base">
                    <PrevIcon />
                  </button>
                )
              }
              if (type === 'next') {
                return (
                  <button className="flex h-base-32 w-base-32 items-center rounded-full bg-gray-base">
                    <NextIcon />
                  </button>
                )
              }
              if (type === 'jump-prev') {
                return <a className="custom-ellipsis">...</a>
              }
              if (type === 'jump-next') {
                return <a className="custom-ellipsis">...</a>
              }
              return originalElement
            }}
          />
        </div>
      )}
    </div>
  )
}

export default OrderList
