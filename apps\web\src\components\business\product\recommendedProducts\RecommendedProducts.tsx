'use client'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  cn,
  storeConfigSelector,
  useCurrentTime,
  useLazyGetCategoryProductsQuery,
  useLazyGetRelatedProductsQuery,
  usePagination,
} from '@ninebot/core'
import { HomeProduct } from '@ninebot/core'
import { useAppSelector } from '@ninebot/core/src/store/hooks'

import { CustomEmpty, ProductCard, ProductCardSkeleton, Skeleton } from '@/components'

type RecommendedProductsProps = {
  sku?: string
  isCartPage?: boolean
  customPageSize?: number
  headerTitleStyle?: string
  productItemContainerStyle?: React.CSSProperties | undefined
  isProductDetailPage?: boolean
  productNameWrapperStyle?: string
  isSearchModal?: boolean
  isSearchPage?: boolean
  closeModal?: () => void
}

/**
 * 产品推荐组件
 */

const RecommendedProducts = ({
  sku = '',
  isCartPage = false,
  customPageSize = 24,
  headerTitleStyle = '',
  productItemContainerStyle = {},
  isSearchPage = false,
  productNameWrapperStyle = '',
  isSearchModal = false,
  closeModal = () => {},
  isProductDetailPage = false,
}: RecommendedProductsProps) => {
  const storeConfig = useAppSelector(storeConfigSelector)
  const getI18nString = useTranslations('Common')
  const { page, pageSize, handlePageNext } = usePagination(1, customPageSize)
  const [getCategoryProducts, { isLoading, isFetching, isUninitialized }] =
    useLazyGetCategoryProductsQuery()
  const [products, setProducts] = useState<HomeProduct[]>([])
  const [hasMore, setHasMore] = useState(false)
  const [isEmpty, setIsEmpty] = useState(false)
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()

  const getProducts = useCallback(
    (categoryUid: string) => {
      if (!categoryUid) {
        return
      }

      getCategoryProducts({
        filters: { category_uid: { eq: categoryUid } },
        pageSize,
        currentPage: page,
        customAttributesV3Filter: {
          filter_attributes: [
            'product_tag',
            'paymeng_method',
            'max_usage_limit_ncoins',
            'is_insurance',
            'insurance_link',
          ],
        },
      })
        .unwrap()
        .then((res) => {
          const items = res?.categories?.items || []
          if (!items.length) {
            setIsEmpty(true)
            return
          }

          const newProducts = items[0]?.products

          setProducts((prevProducts) => {
            const existingSkus = new Set(prevProducts.map((product) => product.sku))

            const filteredNewProducts = newProducts?.items?.filter(
              (product) => !existingSkus.has(product?.sku),
            )

            return [...prevProducts, ...(filteredNewProducts as HomeProduct[])]
          })

          setHasMore(
            Number(newProducts?.page_info?.total_pages) >
              Number(newProducts?.page_info?.current_page),
          )

          setIsEmpty(false)
        })
        .catch(() => {})
    },
    [getCategoryProducts, page, pageSize],
  )

  const [getRelatedProducts, { isLoading: isRelatedLoading }] = useLazyGetRelatedProductsQuery()

  const getRelatedProductsList = useCallback(
    (productSku: string) => {
      if (!productSku) {
        setProducts([])
        return
      }
      getRelatedProducts({
        filter: { sku: { eq: productSku } },
        pageSize: 6,
        currentPage: 1,
        customAttributesV3Filter: {
          filter_attributes: [
            'product_tag',
            'paymeng_method',
            'max_usage_limit_ncoins',
            'is_insurance',
            'insurance_link',
          ],
        },
      })
        .unwrap()
        .then((res) => {
          const relatedProductsData = res?.products?.items?.[0]?.related_products || []
          if (!relatedProductsData.length) {
            setIsEmpty(true)
            return
          }
          setProducts(relatedProductsData as HomeProduct[])
          setHasMore(false)
          setIsEmpty(false)
        })
        .catch(() => {})
    },
    [getRelatedProducts],
  )

  useEffect(() => {
    if (sku) {
      getRelatedProductsList(sku)
    } else {
      getProducts(storeConfig?.guess_like_category_uid || '')
    }
  }, [getProducts, getRelatedProductsList, sku, storeConfig])

  const handleLoadMore = useCallback(() => {
    setHasMore(false)
    handlePageNext()
  }, [handlePageNext])

  const title = useMemo(() => {
    return isSearchModal || isSearchPage
      ? getI18nString('you_may_like')
      : getI18nString('recommend_for_you')
  }, [isSearchModal, isSearchPage, getI18nString])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  const customWidth = useMemo(() => {
    if (isSearchModal) {
      return 113
    }

    return '25%'
  }, [isSearchModal])

  const showInitSkeleton = useMemo(() => {
    return sku ? isRelatedLoading : isUninitialized
  }, [isUninitialized, isRelatedLoading, sku])

  if (!products.length && !sku && !isCartPage) {
    if (isLoading || isRelatedLoading) {
      return (
        <div className="mt-8 flex flex-col">
          <Skeleton
            style={{
              borderRadius: 4,
              height: isSearchModal ? 24 : 40,
              width: customWidth,
              marginBottom: 16,
            }}
          />
          <ProductCardSkeleton length={isSearchModal ? 5 : 8} perRow={isSearchModal ? 5 : 4} />
        </div>
      )
    }

    return null
  }

  return (
    <div className="mt-48 flex flex-col">
      {showInitSkeleton ? (
        <>
          <Skeleton
            style={{
              borderRadius: 4,
              height: isSearchModal ? 24 : 40,
              width: customWidth,
              marginBottom: 16,
            }}
          />
          <ProductCardSkeleton length={isSearchModal ? 5 : 8} perRow={isSearchModal ? 5 : 4} />
        </>
      ) : (
        <>
          <div
            className={cn(
              'font-miSansDemiBold450 !leading-[120%]',
              isSearchModal ? 'mb-8 text-[20px]' : 'mb-base-32 text-[40px]',
              headerTitleStyle,
            )}>
            {title}
          </div>

          <div className="mt-0">
            {products.length > 0 ? (
              <>
                <div className={cn('grid grid-cols-4 gap-base-16', isSearchModal && 'grid-cols-5')}>
                  {products.map((item) => (
                    <ProductCard
                      key={item.sku}
                      product={item}
                      currentTime={currentTime}
                      containerStyle={{
                        borderRadius: 12,
                        ...productItemContainerStyle,
                      }}
                      nameWrapperStyle={productNameWrapperStyle}
                      isSearchModal={isSearchModal}
                      closeModal={closeModal}
                    />
                  ))}
                </div>
                {isFetching
                  ? null
                  : hasMore &&
                    !isProductDetailPage && (
                      <div className="mb-[57px] mt-[28px] flex items-center justify-center">
                        <div
                          className="flex h-[32px] w-[82px] items-center justify-center rounded-[101px] bg-[#F3F3F4]"
                          onClick={handleLoadMore}>
                          <div className="font-miSansSemibold520 text-[13px] text-[#000000]">
                            {getI18nString('more')}
                          </div>
                        </div>
                      </div>
                    )}
              </>
            ) : null}
            {isEmpty ? <CustomEmpty /> : null}
          </div>
        </>
      )}
    </div>
  )
}

export default RecommendedProducts
