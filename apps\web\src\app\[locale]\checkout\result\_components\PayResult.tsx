'use client'
import { useCallback, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import {
  IconPlus,
  NCoinView,
  Price,
  ROUTE,
  useGetOrderDetailQuery,
  useNavigate,
  useOrderDetail,
} from '@ninebot/core'
import { resetCheckout } from '@ninebot/core/src/store'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'
import clsx from 'clsx'

import { EmptyData, Skeleton } from '@/components'

import { Failed, Success } from '../../_icons'

interface PayResultProps {
  isSuccess?: boolean
  orderId: string
}

export default function PayResult({ isSuccess = false, orderId }: PayResultProps) {
  const getI18nString = useTranslations('Common')
  const dispatch = useAppDispatch()

  const { openPage } = useNavigate()

  // 获取订单详情
  const {
    data: response,
    isLoading,
    error,
  } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderId },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )

  const orderData = response?.customer?.orders?.items?.[0]

  // 处理订单数据
  const { totalAmount, nCoin, isOnlyNCoin } = useOrderDetail(orderData)

  const handleToHome = useCallback(() => {
    openPage({
      route: ROUTE.home,
      replace: true,
    })
  }, [openPage])

  /**
   * 跳转到订单详情
   */
  const handleToAccountOrderDetail = useCallback(() => {
    openPage({
      route: ROUTE.accountOrderDetail,
      queryParams: {
        orderNumber: orderId,
      },
      replace: true,
    })
  }, [openPage, orderId])

  /**
   * 重新支付事件
   */
  const handleRepayOrder = useCallback(() => {
    openPage({
      route: ROUTE.checkoutPaying,
      queryParams: {
        orderId,
      },
      replace: true,
    })
  }, [orderId, openPage])

  // 重置结账状态
  useEffect(() => {
    dispatch(resetCheckout())
  }, [dispatch])

  // 处理加载状态
  if (isLoading) {
    return <Skeleton style={{ height: '500px' }} />
  }

  // 处理错误状态或数据为空的情况
  if (error || !orderData) {
    return (
      <div className="flex h-[508px] w-full flex-col items-center justify-center rounded-[16px] bg-white px-4 py-8">
        <EmptyData
          isArrowBtnVisible={false}
          btnStyle="bg-[#f3f3f4] p-[20px]"
          pathname={`checkout/result/${orderId}`}
        />
      </div>
    )
  }

  return (
    <div className="flex h-[508px] w-full flex-col items-center justify-center rounded-[16px] bg-white px-4 py-8">
      {/* Success Icon */}
      <div className="mb-base-12">{isSuccess ? <Success /> : <Failed />}</div>

      {/* Success Text */}
      <h1
        className={clsx(
          'mb-base-32 font-miSansDemiBold450 text-[28px] leading-[1.2]',
          isSuccess ? 'text-[#000]' : 'text-primary',
        )}>
        {isSuccess ? getI18nString('pay_success') : getI18nString('pay_error')}
      </h1>

      {/* Payment Amount */}
      <div className="mb-base-12 flex items-center text-center">
        {isOnlyNCoin ? (
          <div className="flex items-center">
            <span className="text-2xl">{getI18nString('n_coin_pay')}：</span>
            <NCoinView number={nCoin} iconStyle={{ size: 20 }} textStyle="text-2xl" />
          </div>
        ) : (
          <div className="flex items-center">
            <span className="text-2xl">
              {isSuccess ? getI18nString('online_pay') : getI18nString('accounts_payable')}：
            </span>
            <Price
              price={totalAmount}
              currencyStyle="text-2xl"
              textStyle="text-2xl"
              fractionStyle="text-2xl"
            />
            {nCoin ? (
              <>
                <div className="mx-[4px]">
                  <IconPlus />
                </div>
                <NCoinView number={nCoin} iconStyle={{ size: 20 }} textStyle="text-2xl" />
              </>
            ) : null}
          </div>
        )}
      </div>

      {/* Order Info Text */}
      <p className="mb-[52px] text-2xl text-gray-3">{getI18nString('account_view_order')}</p>

      {/* Action Buttons */}
      <div className="flex w-full items-center justify-center gap-base-16">
        {isSuccess ? (
          <>
            <Button size="large" className="w-[240px]" onClick={handleToHome}>
              {getI18nString('back_home')}
            </Button>
            <Button
              size="large"
              className="w-[240px]"
              onClick={handleToAccountOrderDetail}
              type="primary">
              {getI18nString('view_order')}
            </Button>
          </>
        ) : (
          <>
            <Button size="large" className="w-[240px]" onClick={handleToAccountOrderDetail}>
              {getI18nString('view_order')}
            </Button>
            <Button size="large" className="w-[240px]" onClick={handleRepayOrder} type="primary">
              {getI18nString('repay')}
            </Button>
          </>
        )}
      </div>
    </div>
  )
}
