'use client'
import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  NCoinView,
  PaymentMethod,
  Price,
  resolveCatchMessage,
  TCatchMessage,
  useGetOrderDetailQuery,
  useLoadingContext,
  useNavigate,
  useOrderDetail,
  useToastContext,
  useUserOrder,
} from '@ninebot/core'
import { ROUTE } from '@ninebot/core/src/constants'
import { resetCheckout } from '@ninebot/core/src/store'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd-mobile'
import clsx from 'clsx'

import { PayMethodPopup } from '@/components'
import { EmptyData } from '@/components'

const SuccessIcon = () => (
  <svg width="40" height="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M44.272 15.4758L41.1665 18.5452C41.1243 18.4154 41.2116 18.6735 41.1665 18.5452C41.7219 20.2574 42.0156 22.1018 42.0156 23.998C42.0156 33.918 33.9356 41.998 24.0156 41.998C14.0956 41.998 6.01562 33.918 6.01562 23.998C6.01562 14.078 14.0756 5.99805 24.0156 5.99805C28.2156 5.99805 32.0956 7.45805 35.1556 9.87805L37.9956 7.03805C34.1956 3.87805 29.3156 1.99805 24.0156 1.99805C11.8756 1.99805 2.01562 11.878 2.01562 23.998C2.01562 36.118 11.8756 45.998 24.0156 45.998C36.1556 45.998 46.0156 36.138 46.0156 23.998C46.0156 20.9722 45.4 18.0913 44.272 15.4758C44.3322 15.6077 44.2146 15.3425 44.272 15.4758Z"
      fill="black"
    />
    <path
      d="M44.7852 10.7832L42 7.99805L23.2148 27.0146L23.2148 29.998L26 29.998L44.7852 10.7832Z"
      fill="black"
    />
    <path
      d="M15.8284 20.1716L13 23L19.5149 30.0149L22.5002 29.9998L22.5149 27.0149L15.8284 20.1716Z"
      fill="black"
    />
  </svg>
)

const FailedIcon = () => (
  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.00261 20.0026C5.00261 11.7183 11.7183 5.00261 20.0026 5.00261C28.2869 5.00261 35.0026 11.7183 35.0026 20.0026C35.0026 28.2869 28.2869 35.0026 20.0026 35.0026C11.7183 35.0026 5.00261 28.2869 5.00261 20.0026ZM20.0026 1.66928C9.87739 1.66927 1.66928 9.87738 1.66928 20.0026C1.66928 30.1278 9.87738 38.3359 20.0026 38.3359C30.1278 38.3359 38.3359 30.1278 38.3359 20.0026C38.3359 9.87738 30.1278 1.66928 20.0026 1.66928ZM21.6706 28.3058L21.6706 18.3632L20.0039 16.7539L18.3372 18.3632L18.3372 28.3058L21.6706 28.3058ZM18.3372 11.6668L18.3372 13.2761L20.0039 14.8854L21.6706 13.2761L21.6706 11.6668L18.3372 11.6668Z"
      fill="#EC1D26"
    />
  </svg>
)

interface PayResultProps {
  isSuccess?: boolean
  orderId: string
  paymentMethodCode?: string
}

export default function PayResult({
  isSuccess = false,
  orderId,
  paymentMethodCode,
}: PayResultProps) {
  const getI18nString = useTranslations('Common')
  const dispatch = useAppDispatch()
  const toast = useToastContext()
  const loadingCtx = useLoadingContext()
  const [paymentVisible, setPaymentVisible] = useState(false)
  const [paymentsList, setPaymentsList] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const { getAvailablePayments } = useUserOrder()
  const { openPage } = useNavigate()

  // 获取订单详情
  const {
    data: response,
    error: queryError,
    isLoading,
  } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderId },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )

  const orderData = response?.customer?.orders?.items?.[0]

  // 处理订单数据
  const { totalAmount, nCoin, isOnlyNCoin } = useOrderDetail(orderData)

  // 错误处理
  useEffect(() => {
    if (queryError) {
      setError(true)
      setLoading(false)
      toast.show({
        icon: 'fail',
        content: getI18nString('load_order_failed'),
      })
    } else if (!isLoading) {
      const hasOrderData = !!orderData

      if (!hasOrderData) {
        setError(true)
      }
      setLoading(false)
    }
  }, [queryError, isLoading, response, orderData, getI18nString, toast])

  const handleToHome = useCallback(() => {
    openPage({
      route: ROUTE.home,
      replace: true,
    })
  }, [openPage])

  /**
   * 跳转到订单详情
   */
  const handleToAccountOrderDetail = useCallback(() => {
    openPage({
      route: ROUTE.accountOrderDetail,
      queryParams: {
        orderNumber: orderId,
      },
      replace: true,
    })
  }, [openPage, orderId])

  /**
   * 重新支付事件
   */
  const handleRepayOrder = useCallback(async () => {
    try {
      const payments = await getAvailablePayments(Number(totalAmount?.value))
      if (payments?.length) {
        setPaymentVisible(true)
        setPaymentsList(payments as PaymentMethod[])
        return
      } else {
        toast.show({
          icon: 'fail',
          content: getI18nString('no_payment_methods'),
        })
      }
    } catch (error) {
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
    }
  }, [getAvailablePayments, totalAmount, getI18nString, toast])

  useEffect(() => {
    dispatch(resetCheckout())
  }, [dispatch])

  useEffect(() => {
    if (loading) {
      loadingCtx.show()
    } else {
      loadingCtx.hide()
    }
  }, [loading, loadingCtx])

  // 错误状态
  if (error) {
    return (
      <div className="flex h-[508px] w-full flex-col items-center justify-center rounded-[16px] bg-white px-4 py-8">
        <EmptyData isArrowBtnVisible={false} btnStyle="bg-[#f3f3f4] p-[20px]" />
      </div>
    )
  }

  if (loading) {
    return null
  }

  return (
    <>
      <div className="flex h-[508px] w-full flex-col items-center justify-center rounded-[16px] bg-white">
        {/*  Icon */}
        <div>{isSuccess ? <SuccessIcon /> : <FailedIcon />}</div>

        {/* Text */}
        <h1
          className={clsx(
            'my-[16px] font-miSansDemiBold450 text-[22px] leading-[1.2]',
            isSuccess ? 'text-[#000]' : 'text-primary',
          )}>
          {isSuccess ? getI18nString('pay_success') : <span>超时未支付，请重新支付</span>}
        </h1>

        {/* Payment Amount */}
        <div className="mb-[8px] flex items-center justify-center gap-1 text-[16px] leading-[1.4] text-black">
          {isOnlyNCoin ? (
            <>
              <span>{getI18nString('n_coin_pay')}：</span>
              <NCoinView number={nCoin} />
            </>
          ) : (
            <>
              <span>
                {isSuccess ? getI18nString('online_pay') : getI18nString('accounts_payable')}：
              </span>
              <Price
                price={totalAmount}
                currencyStyle="text-[16px] leading-[1.4]"
                containerStyle="text-[16px] leading-[1.4]"
                fractionStyle="text-[16px] leading-[1.4]"
              />
              {nCoin ? (
                <>
                  <span className="mx-1">+</span>
                  <NCoinView number={nCoin} />
                </>
              ) : null}
            </>
          )}
        </div>

        {/* Order Info Text */}
        <p className="mb-[32px] text-[16px] leading-[1.4] text-[#86868B]">
          {getI18nString('account_view_order')}
        </p>

        {/* Action Buttons */}
        <div className="flex w-full items-center gap-[12px] px-[40px]">
          {isSuccess ? (
            <>
              <Button className="nb-button w-full" onClick={handleToHome}>
                {getI18nString('back_home')}
              </Button>
              <Button
                className="nb-button w-full"
                color="primary"
                onClick={handleToAccountOrderDetail}>
                {getI18nString('view_order')}
              </Button>
            </>
          ) : (
            <>
              <Button className="nb-button w-full" onClick={handleToAccountOrderDetail}>
                {getI18nString('view_order')}
              </Button>
              <Button className="nb-button w-full" color="primary" onClick={handleRepayOrder}>
                {getI18nString('repay')}
              </Button>
            </>
          )}
        </div>
      </div>

      {/** 支付方式弹窗 */}
      <PayMethodPopup
        popupVisible={paymentVisible}
        tempOrderInfo={{
          orderId,
          totalAmount,
          defaultPayment: paymentMethodCode,
        }}
        paymentsList={paymentsList}
        closePopup={() => {
          setPaymentVisible(false)
        }}
      />
    </>
  )
}
