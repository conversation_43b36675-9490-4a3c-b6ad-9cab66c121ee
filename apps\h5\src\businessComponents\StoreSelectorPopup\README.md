# StoreSelectorPopup 门店选择器组件

## 概述

`StoreSelectorPopup` 是一个功能完整的门店选择器弹窗组件，支持自动定位、手动地址选择和门店列表展示。

## 组件结构

```
StoreSelectorPopup/
├── index.tsx          # 主组件
├── AddressSelector.tsx # 地址选择器
├── StoreList.tsx      # 门店列表
├── StoreItem.tsx      # 门店列表项
├── StoreEmpty.tsx     # 空状态组件
└── README.md          # 说明文档
```

## 主要功能

### 1. 自动定位功能

- 获取用户当前位置
- 逆地理编码获取地址信息
- 定位权限引导和设置

### 2. 地址选择功能

- 省市区三级联动选择
- 支持默认值设置
- 数据缓存和加载优化

### 3. 门店列表功能

- 根据地址和位置获取门店
- 分页加载更多
- 门店选择和状态管理

### 4. 状态管理

- 统一的状态枚举管理
- 清晰的状态转换逻辑
- 完善的错误处理

## 组件状态

```typescript
enum PopupState {
  LOADING = 'loading', // 加载中
  LOCATION_DENIED = 'location_denied', // 定位被拒绝
  ADDRESS_SELECTION = 'address_selection', // 地址选择
  STORE_SELECTION = 'store_selection', // 门店选择
}
```

## 本地存储键名

```typescript
const STORAGE_KEYS = {
  USER_LOCATION: 'pdp_user_location', // 用户位置信息
  ADDRESS: 'pdp_address', // 选择的地址
  SELECT_PRODUCT_OPTION_OPEN: 'select_product_option_open', // 是否打开加购弹窗
}
```

## 错误处理

组件包含完善的错误处理机制：

1. **定位失败**：自动降级到手动地址选择
2. **网络错误**：显示友好的错误提示
3. **数据异常**：提供默认状态和重试机制
4. **权限拒绝**：引导用户开启定位权限

## 注意事项

1. 确保 `@ninebot/core` 包中包含必要的类型定义
2. 需要配置高德地图 API 用于逆地理编码
3. 确保国际化配置中包含必要的文案
4. 注意定位权限的浏览器兼容性
