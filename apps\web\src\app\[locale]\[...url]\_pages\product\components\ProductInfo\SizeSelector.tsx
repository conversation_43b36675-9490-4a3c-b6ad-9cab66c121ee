'use client'
import React from 'react'
import { cn } from '@ninebot/core'

import { SoldOutTag } from '@/components'
import type { ProductStatus } from '@/types/product'
interface OptionItem {
  value_index: number
  label: string
}

interface SizeSelectorProps {
  optionItems: OptionItem[]
  id: string
  productStatus: ProductStatus
  onSelectionChange: (item: OptionItem, id: string) => void
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  optionItems,
  id,
  productStatus,
  onSelectionChange,
}) => {
  if (!productStatus) return null
  const { optionSelections, outOfStockVariants = [], isEverythingOutOfStock } = productStatus
  const optionId = optionSelections?.get(id)
  // 提前计算缺货数组的 Set
  const outOfStockSet = new Set(outOfStockVariants.flat())
  return (
    <div
      className={cn('grid grid-cols-3 gap-base-16', {
        'grid-cols-2': optionItems.length < 2,
      })}>
      {optionItems.map((item) => {
        const isOptionOutOfStock = outOfStockSet.has(item.value_index)
        return (
          <button
            key={item.value_index}
            disabled={isOptionOutOfStock}
            className={cn(
              'font-miSansRegular380 relative flex h-[48px] max-w-[156px] items-center justify-center rounded-base border p-base text-[16px] leading-[140%] transition-colors',
              optionId === item.value_index
                ? 'border-primary bg-[#DA291C0D] text-primary'
                : 'border-[#F8F8F9] bg-[#F8F8F9]',
              isOptionOutOfStock && 'cursor-not-allowed',
            )}
            onClick={() => {
              if (
                !(isEverythingOutOfStock || isOptionOutOfStock) &&
                optionId !== item.value_index
              ) {
                onSelectionChange(item, id)
              }
            }}>
            <span className={cn('text-center', isOptionOutOfStock && 'text-gray-400')}>
              {item.label}
            </span>
            {isOptionOutOfStock && (
              <div className="absolute -top-2 right-0">
                <SoldOutTag />
              </div>
            )}
          </button>
        )
      })}
    </div>
  )
}

export default SizeSelector
