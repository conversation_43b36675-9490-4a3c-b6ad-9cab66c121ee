'use client'
import { useEffect, useMemo, useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useGetFooterNewsQuery, useGetSiteConfigQuery } from '@ninebot/core'
import { FooterNewsCategory, SiteConfig } from '@ninebot/core/src/graphql/generated/graphql'

import { NineBot, Region } from '@/components'
import { Link } from '@/i18n/navigation'

const Footer = () => {
  const [footer, setFooter] = useState<FooterNewsCategory[]>([])
  const [footerSiteConfig, setFooterSiteConfig] = useState<SiteConfig>({})
  const { currentData } = useGetFooterNewsQuery({})
  const { currentData: siteConfig } = useGetSiteConfigQuery({})
  const pathName = usePathname()
  const isPayingPage = ['/checkout/paying', '/checkout/result'].includes(pathName)

  useEffect(() => {
    if (currentData?.home_footer_news && currentData?.home_footer_news?.length > 0) {
      setFooter(currentData?.home_footer_news as FooterNewsCategory[])
    }
  }, [currentData])

  useEffect(() => {
    if (siteConfig?.site_config) {
      setFooterSiteConfig(siteConfig?.site_config as SiteConfig)
    }
  }, [siteConfig])

  const legalLinks = useMemo(() => {
    const links: { name: string; url: string }[] = []
    if (footerSiteConfig.footer_links && Array.isArray(footerSiteConfig.footer_links)) {
      footerSiteConfig.footer_links.forEach((link) => {
        if (link?.label && link?.url) {
          links.push({
            name: link.label,
            url: link.url.url || '/' + link.url.value || '',
          })
        }
      })
    }
    return links
  }, [footerSiteConfig])

  if (isPayingPage) {
    return (
      <footer className="w-full bg-[#F8F8F9] py-base-16">
        <div className="max-container mx-auto !mb-0 flex w-full items-center justify-center">
          <span className="flex items-center gap-4 text-center text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="76"
              height="16"
              viewBox="0 0 76 16"
              fill="none">
              <g clipPath="url(#clip0_56623_377917)">
                <path
                  d="M8.48611 0C4.07296 0 0.5 3.57917 0.5 8C0.5 12.4208 4.07296 16 8.48611 16C12.8993 16 16.4722 12.4208 16.4722 8C16.4722 3.57917 12.8993 0 8.48611 0ZM3.25822 7.42189C3.25822 7.19235 3.44493 7.00531 3.67408 7.00531H8.84256C9.0717 7.00531 9.25841 7.19235 9.25841 7.42189C9.25841 7.65144 9.0717 7.83847 8.84256 7.83847H3.67408C3.44493 7.83847 3.25822 7.65144 3.25822 7.42189ZM11.0491 8.11902C11.0491 8.11902 9.83552 12.0213 9.5894 12.7864C9.24144 13.8746 8.05328 14.3677 7.05184 13.9086C6.0419 13.4495 6.22013 12.4973 6.30499 12.2168L6.82269 10.5505C6.96697 10.0999 7.00091 9.83634 6.79723 9.52179C6.5596 9.19022 6.13526 9.20723 5.98249 9.20723H5.38842C5.15927 9.20723 4.97256 9.02019 4.97256 8.79065C4.97256 8.56111 5.15927 8.37407 5.38842 8.37407H7.83262C8.39276 8.37407 8.66433 8.68863 8.40124 9.20723L7.09427 11.8002C6.83966 12.2933 6.83118 12.9139 7.39131 13.152C8.08723 13.4495 8.67282 12.9394 8.88499 12.2678C8.98683 11.9532 10.2683 7.85547 10.2683 7.85547C10.3787 7.49841 10.472 7.08183 10.2514 6.75877C10.0392 6.46121 9.58091 6.45271 9.42815 6.45271H4.80282C4.57368 6.45271 4.38697 6.26568 4.38697 6.03613C4.38697 5.80659 4.57368 5.61955 4.80282 5.61955H9.42815C9.60637 5.61955 10.4296 5.60255 10.9133 6.22317C11.2698 6.69926 11.3037 7.31987 11.0491 8.11902ZM10.6503 5.21148C9.87795 5.21148 9.25841 4.58236 9.25841 3.81722C9.25841 3.04357 9.88644 2.42295 10.6503 2.42295C11.4226 2.42295 12.0421 3.04357 12.0421 3.81722C12.0421 4.59086 11.4226 5.21148 10.6503 5.21148Z"
                  fill="#6E6E73"
                />
                <path
                  d="M26.9102 2.96609V4.30084L28.1068 4.02028C28.3105 3.96928 28.4548 3.79074 28.4548 3.5782V2.25195L27.2581 2.53251C27.0544 2.57501 26.9102 2.75355 26.9102 2.96609Z"
                  fill="#6E6E73"
                />
                <path
                  d="M39.7344 8.39063C39.7344 8.39063 39.7344 7.43845 39.7344 6.57129C39.7344 5.26204 38.682 4.60742 37.375 4.60742C36.068 4.60742 35.0072 5.26204 35.0157 6.57129V10.329C35.0072 11.6382 36.068 12.2929 37.375 12.2929C38.682 12.2929 39.7428 11.6382 39.7344 10.329V9.32581L38.1813 9.69137V10.346C38.1813 10.7711 37.8248 10.9836 37.375 10.9836C36.9252 10.9836 36.5688 10.7711 36.5688 10.346V9.12177L39.7344 8.39063ZM36.5773 6.57129C36.5773 6.14621 36.9337 5.93367 37.3835 5.93367C37.8333 5.93367 38.1898 6.14621 38.1898 6.57129C38.1898 6.78383 38.1898 7.41295 38.1898 7.41295L36.5773 7.78702C36.5773 7.77852 36.5773 6.86885 36.5773 6.57129Z"
                  fill="#6E6E73"
                />
                <path
                  d="M23.8293 4.62397C22.6751 4.70898 22.4545 5.12556 21.2578 5.06605V12.2244H22.8109V6.57083C22.8109 6.14575 23.1673 5.93321 23.6172 5.93321C24.067 5.93321 24.4234 6.14575 24.4234 6.57083V12.2244H25.9765V6.63034C25.985 5.3211 25.1448 4.52195 23.8293 4.62397Z"
                  fill="#6E6E73"
                />
                <path
                  d="M26.9102 5.61717V12.2229H28.4632V4.89453L27.2666 5.17508C27.0544 5.21759 26.9102 5.40463 26.9102 5.61717Z"
                  fill="#6E6E73"
                />
                <path
                  d="M31.7461 4.61523C30.4391 4.61523 29.3783 5.26986 29.3868 6.5791V12.2242H30.9399V6.5706C30.9399 6.14552 31.2963 5.93298 31.7461 5.93298C32.1959 5.93298 32.5524 6.14552 32.5524 6.5706V12.2242H34.1054V6.5791C34.1139 5.26986 33.0531 4.61523 31.7461 4.61523Z"
                  fill="#6E6E73"
                />
                <path
                  d="M43.2108 4.6154C42.8543 4.6324 42.4809 4.73442 42.2009 4.88745V2.25195L41.0042 2.53251C40.8005 2.58352 40.6562 2.76205 40.6562 2.97459V11.8503C41.8444 11.7907 42.0736 12.2158 43.2278 12.2923C44.5432 12.3859 45.3749 11.5952 45.3749 10.286C45.3749 9.35078 45.3749 7.47193 45.3749 6.53676C45.358 5.27002 44.5347 4.53888 43.2108 4.6154ZM43.8049 10.3455C43.8049 10.7706 43.4484 10.9831 42.9986 10.9831C42.5488 10.9831 42.1924 10.7706 42.1924 10.3455V6.57076C42.1924 6.14568 42.5488 5.93314 42.9986 5.93314C43.4484 5.93314 43.8049 6.14568 43.8049 6.57076V10.3455Z"
                  fill="#6E6E73"
                />
                <path
                  d="M53.4431 4.13049V3.19531L52.2465 3.47587C52.0428 3.52687 51.8985 3.70541 51.8985 3.91795V10.2686C51.89 11.5779 52.9509 12.2325 54.2578 12.2325H55.0386V10.9148H54.2578C53.808 10.9148 53.4516 10.7022 53.4516 10.2771V6.36641L55.0301 6.00084V4.67459L53.4516 5.04016V4.13049H53.4431Z"
                  fill="#6E6E73"
                />
                <path
                  d="M48.6367 4.61523C47.3298 4.61523 46.2689 5.26986 46.2774 6.5791V10.3368C46.2689 11.6461 47.3298 12.3007 48.6367 12.3007C49.9437 12.3007 51.0046 11.6461 50.9961 10.3368V6.5791C51.0046 5.26986 49.9437 4.61523 48.6367 4.61523ZM49.443 10.3453C49.443 10.7704 49.0865 10.9829 48.6367 10.9829C48.1869 10.9829 47.8305 10.7704 47.8305 10.3453V6.5706C47.8305 6.14552 48.1869 5.93298 48.6367 5.93298C49.0865 5.93298 49.443 6.14552 49.443 6.5706V10.3453Z"
                  fill="#6E6E73"
                />
                <path
                  d="M71.5482 5.35598H68.7136V4.3953H72.1932V6.37617H74.0688V3.37511H66.8295V6.37617H71.5482V5.35598ZM75.1551 8.02548V7.00529H65.7517V8.02548H67.1351L66.7532 9.67479H72.0065C71.4803 10.729 70.5977 11.1966 70.1479 11.3496L72.015 12.4378C72.0405 12.4293 74.0179 11.5366 74.1282 8.6461H69.0022L69.1465 8.01698H75.1551V8.02548ZM64.572 10.2699C64.572 9.08818 64.572 4.68436 64.572 4.68436H60.6936C60.702 4.19126 60.7105 3.69817 60.7105 3.20508H58.8349C58.8349 3.72368 58.8349 4.21677 58.8265 4.68436H57.0612V5.82357H58.784C58.6482 8.17851 58.1645 9.97235 56.5859 11.2561L58.504 12.3613C60.091 10.6185 60.5153 8.22952 60.6511 5.82357H62.6965C62.6965 5.82357 62.6965 9.35173 62.6965 10.746C62.6965 12.6333 65.7347 12.2763 65.7347 12.2763L66.1251 10.576C66.1251 10.5675 64.572 11.1371 64.572 10.2699Z"
                  fill="#6E6E73"
                />
              </g>
              <defs>
                <clipPath id="clip0_56623_377917">
                  <rect width="75" height="16" fill="white" transform="translate(0.5)" />
                </clipPath>
              </defs>
            </svg>
            <span className="text-sm leading-[16px] text-[#6E6E73]">
              {footerSiteConfig.copyright_info ||
                '© 2025 Ninebot Ltd. 纳恩博（北京）科技有限公司 2012-2024 京ICP备17025273号-2'}
            </span>
          </span>
        </div>
      </footer>
    )
  }

  return (
    <footer className="min-w-[1024px] bg-gray-base pb-[16px] pt-[32px]">
      <div className="max-container-no-mb">
        {/* Logo and Navigation */}
        <div className="mb-[28px] flex items-center gap-base-16">
          {/* <Logo isBig /> */}
          <NineBot />
          {/* <IconArrow rotate={-90} size={12} />
          <span className="ml-2 font-miSansMedium380 text-[14px] leading-[120%] text-font-primary">
            滑板车
          </span> */}
        </div>

        {/* Main Footer Content */}
        <div className="mb-base-64 grid grid-cols-6 gap-[96px] pb-16">
          {footer.map((item) => (
            <div key={item.category_sort}>
              <h3 className="mb-[16px] font-miSansSemibold520 text-[12px] leading-[16px] text-font-primary">
                {item.category_title}
              </h3>
              <ul className="space-y-2">
                {item?.contains_news?.map((item) => (
                  <li key={item?.news_sort}>
                    <Link
                      href={`${item?.redirect?.url}`}
                      className="font-miSansMedium380 text-[12px] leading-[100%] text-[#6E6E73] hover:text-gray-900">
                      {item?.news_title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mb-base flex items-center justify-between">
          {/* Legal Links */}
          <div className="flex flex-wrap gap-base-16">
            {legalLinks.map((link, index) => (
              <div key={index} className="flex items-center">
                <Link
                  href={link.url}
                  target="_blank"
                  className="font-miSansMedium380 text-[12px] leading-[16px] text-[#6E6E73] hover:text-gray-900">
                  {link.name}
                </Link>
                {index !== legalLinks.length - 1 && (
                  <div className="h-[16px] w-[16px] border-r border-[#00000026]"></div>
                )}
              </div>
            ))}
          </div>

          {/* Social Media Icons */}
          <div className="flex gap-base-16">
            {footerSiteConfig.footer_social && Array.isArray(footerSiteConfig.footer_social) && (
              <>
                {footerSiteConfig.footer_social.map((social) => {
                  return (
                    <Link
                      key={social?.label}
                      href={social?.url?.url || `/${social?.url?.value}`}
                      target="_blank"
                      className="hover:text-gray-900">
                      <Image
                        src={social?.logo || ''}
                        alt={social?.label || ''}
                        width={16}
                        height={16}
                      />
                    </Link>
                  )
                })}
              </>
            )}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-[#00000026] pt-base">
          {/* Copyright */}
          <div className="flex items-center justify-between py-[11px] text-[12px] leading-[16px] text-[#6E6E73]">
            {footerSiteConfig.copyright_info || 'Copyright © Ninebot Inc. All rights reserved.'}
            {/* Language Selector */}
            <div className="flex items-center gap-[4px] font-miSansMedium380 text-[12px] leading-[16px] text-[#6E6E73]">
              <Region />
              <span>中国大陆</span>
              <span>/</span>
              <span>简体中文</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
