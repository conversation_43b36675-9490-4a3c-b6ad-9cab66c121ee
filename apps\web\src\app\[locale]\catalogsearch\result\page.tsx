'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  HomeProduct,
  // resolveCatchMessage,
  storeConfigSelector,
  // TCatchMessage,
  TRACK_EVENT,
  updateProductList,
  // useLazySearchRelationWordsQuery,
  usePagination,
  useSearchProductsQuery,
  // useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useAppSelector } from '@ninebot/core/src/store/hooks'

import {
  ProductCardSkeleton,
  ProductList,
  RecommendProducts,
  SearchInput,
  Skeleton,
} from '@/components'
import { useRouter } from '@/i18n/navigation'

/**
 * 搜索结果页面
 */
const Page = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const getI18nString = useTranslations('Common')
  // const toast = useToastContext()
  const [searchValue, setSearchValue] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [options, setOptions] = useState<{ value: string; label: JSX.Element }[]>([])

  const storeConfig = useAppSelector(storeConfigSelector)
  const defaultSearchWord = storeConfig?.default_search_words || ''

  // const [searchRelationWords] = useLazySearchRelationWordsQuery()
  const { reportEvent } = useVolcAnalytics()

  const [isLoading, setIsLoading] = useState(true)
  const { page, pageSize, handlePageNext } = usePagination(1, 12)
  const { currentData: searchProductData, isFetching } = useSearchProductsQuery(
    {
      search: searchQuery,
      filter: {
        name: {
          match: searchQuery,
        },
      },
      pageSize,
      currentPage: page,
      customAttributesV3Filter: {
        filter_attributes: [
          'product_tag',
          'paymeng_method',
          'max_usage_limit_ncoins',
          'is_insurance',
          'insurance_link',
        ],
      },
    },
    {
      skip: !searchQuery,
    },
  )

  const [productList, setProductList] = useState<HomeProduct[]>([])
  const [total, setTotal] = useState(0)
  const [hasMore, setHasMore] = useState(false)

  useEffect(() => {
    if (isFetching) {
      setIsLoading(true)
    } else {
      const timeoutId = setTimeout(() => {
        setIsLoading(false)
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [isFetching])

  useEffect(() => {
    if (searchProductData?.products?.items && searchProductData.products.items?.length > 0) {
      setTotal(searchProductData.products.total_count as number)

      if (page === 1) {
        setProductList(searchProductData.products.items as HomeProduct[])
      } else {
        setProductList((prevProducts) =>
          updateProductList(prevProducts, searchProductData?.products?.items as HomeProduct[]),
        )
      }

      // 判断是否有更多数据可加载
      const pageInfo = searchProductData.products.page_info
      if (pageInfo) {
        setHasMore((pageInfo?.total_pages || 1) > (pageInfo?.current_page || 1))
      }
    } else if (page === 1) {
      setTotal(0)
      setProductList([])
    }
  }, [searchProductData, page])

  // 从URL获取搜索关键词并设置初始值
  useEffect(() => {
    const q = searchParams.get('q')
    if (q) {
      setSearchValue(q)
      setSearchQuery(q)
    }
  }, [searchParams])

  // 处理输入搜索
  const handleSearch = async (value: string) => {
    setSearchValue(value)
    setOptions([])
    // 搜索逻辑
    // if (!!value) {
    //   try {
    //     const res = await searchRelationWords({
    //       searchWords: value,
    //       currentPage: 1,
    //       pageSize: 9999,
    //     }).unwrap()

    //     const relationWords = res.search_relation_words?.items

    //     if (relationWords && relationWords?.length > 0) {
    //       const newRelationWords = relationWords.filter((item) => item?.type === 'PRODUCT')
    //       if (newRelationWords && newRelationWords?.length > 0) {
    //         setOptions(
    //           newRelationWords.map((item) => ({
    //             value: item?.words || '',
    //             label: (
    //               <div
    //                 dangerouslySetInnerHTML={{
    //                   __html: item?.words
    //                     ? item?.words.replace(
    //                         new RegExp(value, 'g'),
    //                         `<span style="color: red">${value}</span>`,
    //                       )
    //                     : '',
    //                 }}
    //               />
    //             ),
    //           })),
    //         )
    //       } else {
    //         setOptions([])
    //       }
    //     } else {
    //       setOptions([])
    //     }
    //   } catch (error) {
    //     toast.show({
    //       icon: 'fail',
    //       content: resolveCatchMessage(error as TCatchMessage) as string,
    //     })
    //   }
    // } else {
    //   setOptions([])
    // }
  }

  // 处理搜索提交
  const handleSearchSubmit = (value: string) => {
    if (value.trim()) {
      reportEvent(TRACK_EVENT.shop_search_result_exposure, {
        search_word: value,
      })
      router.push(`/catalogsearch/result?q=${value}`)
    }
  }

  return (
    <div className="max-container-no-mb mb-48">
      {/* 搜索框 */}
      <div className="my-8">
        <SearchInput
          searchValue={searchValue}
          options={options}
          onSearch={handleSearch}
          onChange={setSearchValue}
          onClose={() => router.push('/')}
          onSubmit={handleSearchSubmit}
          placeholder={defaultSearchWord}
        />
      </div>

      {/* 搜索结果 */}
      <div className="pt-base-32">
        {isLoading || isFetching ? (
          <div className="flex flex-1 flex-col">
            <Skeleton
              style={{
                height: 20,
                width: 206,
                borderRadius: 4,
                marginBottom: 20,
              }}
            />
            <ProductCardSkeleton length={8} perRow={4} />
          </div>
        ) : (
          <>
            {total > 0 && (
              <div className="mb-16 font-miSansMedium380 text-[18px] leading-[120%]">
                {getI18nString('related_results', { key: searchQuery, key2: total })
                  .split(new RegExp(`(${searchQuery})`, 'gi'))
                  .map((part, index) => (
                    <span
                      key={index}
                      className={
                        part.toLowerCase() === searchQuery.toLowerCase()
                          ? 'font-miSansSemibold520 text-[20px] leading-[120%] text-[#000000]'
                          : ''
                      }>
                      {part}
                    </span>
                  ))}
              </div>
            )}
            <ProductList
              products={productList}
              isSearchResult
              searchTerm={searchQuery}
              currentPage={page}
              currentPageSize={pageSize}
              handlePageNext={handlePageNext}
              hasMore={hasMore}
              total={total}
            />
          </>
        )}

        {!(isLoading || isFetching) ? <RecommendProducts isSearchPage /> : null}
      </div>
    </div>
  )
}

export default Page
