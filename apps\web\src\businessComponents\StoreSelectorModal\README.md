# 门店选择器组件 (StoreSelectorModal)

## 概述

门店选择器是一个功能完整的模态框组件，支持用户通过定位或手动选择地址来查找和选择附近的门店。该组件集成了地图显示、地址选择、门店列表等功能。

## 功能特性

### 🎯 核心功能

- **定位获取**: 自动获取用户当前位置
- **地址选择**: 省市区三级联动选择
- **门店列表**: 展示附近门店信息
- **地图显示**: 可视化门店位置分布
- **门店选择**: 选择并确认门店

### 🔄 状态管理

- **定位权限状态**: 处理定位权限授权
- **地址选择状态**: 省市区选择流程
- **门店选择状态**: 门店列表展示和选择

## 组件结构

```
StoreSelectorModal/
├── index.tsx          # 主组件
├── StoreList.tsx      # 门店列表组件
├── AddressSelector.tsx # 地址选择器组件
└── README.md          # 文档说明
```

## Props 说明

### StoreSelectorModal

| 属性                   | 类型                                     | 必填 | 说明                 |
| ---------------------- | ---------------------------------------- | ---- | -------------------- |
| `doorVisible`          | `boolean`                                | ✅   | 控制模态框显示/隐藏  |
| `setDoorVisible`       | `(visible: boolean) => void`             | ✅   | 设置模态框显示状态   |
| `selectStore`          | `StoreListItem \| null`                  | ✅   | 当前选中的门店       |
| `setSelectStore`       | `(store: StoreListItem \| null) => void` | ✅   | 设置选中的门店       |
| `setVisibleAddCartPop` | `(visible: boolean) => void`             | ✅   | 设置加购弹窗显示状态 |
| `productId`            | `string`                                 | ✅   | 产品ID               |

### StoreList

| 属性            | 类型                                     | 必填 | 说明               |
| --------------- | ---------------------------------------- | ---- | ------------------ |
| `store`         | `StoreList`                              | ✅   | 门店列表数据       |
| `setStore`      | `(stores: StoreList) => void`            | ✅   | 设置门店列表       |
| `setBtnLoading` | `(loading: boolean) => void`             | ✅   | 设置按钮加载状态   |
| `curStore`      | `StoreListItem \| null`                  | ✅   | 当前选中的门店     |
| `setCurStore`   | `(store: StoreListItem \| null) => void` | ✅   | 设置当前选中的门店 |
| `productId`     | `number \| string`                       | ✅   | 产品ID             |
| `address`       | `PdpAddress \| undefined`                | ❌   | 地址信息           |

### AddressSelector

| 属性           | 类型                                                               | 必填 | 说明         |
| -------------- | ------------------------------------------------------------------ | ---- | ------------ |
| `defaultValue` | `{ province?: string; city?: string; district?: string }`          | ❌   | 默认地址值   |
| `onSelect`     | `(province: AreaItem, city: AreaItem, district: AreaItem) => void` | ❌   | 地址选择回调 |

## 状态流转

```
初始化
  ↓
有定位信息 → 逆地理编码 → 门店选择状态
  ↓
无定位权限 → 定位权限状态
  ↓
获取定位 → 地址选择状态 → 门店选择状态
```

## 注意事项

1. **定位权限**: 需要用户授权定位权限才能使用自动定位功能
2. **网络依赖**: 地址数据和门店数据需要网络请求
3. **错误降级**: 定位失败时自动降级到手动地址选择
