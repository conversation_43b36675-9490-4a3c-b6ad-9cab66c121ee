'use client'

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import { HomeProduct, type RedirectType, useCurrentTime } from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { Button } from 'antd'

import { CardGrid, IconArrow, ProductCard, ProductCardSkeleton } from '@/components'

import ProductFilter from '../ProductFilter'

import ProductEmpty from './ProductEmpty'
interface ProductListProps {
  hasCategories?: boolean
  isSearchResult?: boolean
  products?: HomeProduct[]
  searchTerm?: string
  currentPage?: number
  currentPageSize?: number
  handlePageNext?: () => void
  total?: number
  hasMore?: boolean
  sort?: {
    [key: string]: 'ASC' | 'DESC'
  }
  setSort?: React.Dispatch<
    React.SetStateAction<{
      [key: string]: 'ASC' | 'DESC'
    }>
  >
  banner?: {
    title?: string | null | undefined
    subtitle?: string | null | undefined
    image?: string | null | undefined
    description?: string | null | undefined
    buttonText?: string | null | undefined
    url?:
      | {
          type?: RedirectType | null
          url?: string | null
          value?: string | null
        }
      | null
      | undefined
    widthRatio: number | null | undefined
    heightRatio: number | null | undefined
  } | null
  isDigital?: boolean
  onlyMyCar?: boolean
  setOnlyMyCar?: (onlyMyCar: boolean) => void
  isLoadingMore?: boolean
}

export default function ProductList({
  hasCategories = false,
  isSearchResult = false,
  products = [],
  searchTerm = '',
  currentPage = 1,
  currentPageSize = 12,
  handlePageNext = () => {},
  total,
  hasMore = false,
  sort,
  setSort,
  banner,
  isDigital,
  onlyMyCar,
  setOnlyMyCar,
  isLoadingMore = false,
}: ProductListProps) {
  const { timestamp: currentTime, fetchCurrentTime } = useCurrentTime()
  const [scrollPosition, setScrollPosition] = useState(0)
  const [previousProductsLength, setPreviousProductsLength] = useState(0)

  const getI18nString = useTranslations('Web')
  const { openPage } = useNavigate()
  // 计算当前页的商品
  const currentProducts = useMemo(() => {
    // 加载更多模式：显示从开始到当前页的所有数据
    return products.slice(0, currentPage * currentPageSize)
  }, [products, currentPage, currentPageSize])

  // 加载更多处理函数
  const handleLoadMore = useCallback(() => {
    // 记录当前滚动位置和商品数量
    setScrollPosition(window.scrollY)
    setPreviousProductsLength(products.length)

    // 调用父组件的加载更多函数
    handlePageNext()
  }, [handlePageNext, products.length])

  // 监听产品数据变化，恢复滚动位置
  useEffect(() => {
    if (isLoadingMore === false && products.length > previousProductsLength && scrollPosition > 0) {
      // 数据加载完成且有新数据时，恢复滚动位置
      requestAnimationFrame(() => {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'auto', // 使用 auto 避免平滑滚动造成的延迟
        })
        // 重置状态
        setScrollPosition(0)
        setPreviousProductsLength(0)
      })
    }
  }, [isLoadingMore, products.length, previousProductsLength, scrollPosition])

  useEffect(() => {
    fetchCurrentTime()
  }, [fetchCurrentTime])

  return (
    <div className="w-full">
      {!isSearchResult && (
        <ProductFilter
          total={total || products.length}
          hasCategories={hasCategories}
          sort={sort}
          setSort={setSort}
          isDigital={isDigital || false}
          onlyMyCar={onlyMyCar || false}
          setOnlyMyCar={setOnlyMyCar || (() => {})} // 默认设置为空函数
        />
      )}
      {/* Banner */}
      {!isSearchResult && banner?.image ? (
        <div className="relative my-base-32 w-full rounded-[12px] bg-gray-base">
          {banner.image && (
            <>
              <Image
                style={{
                  overflow: 'hidden',
                  borderRadius: 12,
                }}
                width={1440}
                height={576}
                src={banner.image}
                alt={banner?.title || ''}
                priority
              />
              <div className="absolute left-[14%] top-[19%] text-white">
                {banner?.subtitle ? (
                  <div className="mb-8 font-miSansMedium380 text-[20px] leading-[120%]">
                    {banner.subtitle}
                  </div>
                ) : null}
                {banner?.title ? (
                  <div className="mb-8 font-miSansSemibold520 text-[64px] leading-[120%]">
                    {banner.title}
                  </div>
                ) : null}
                {banner?.description ? (
                  <div className="font-miSansMedium380 text-[24px] leading-[140%]">
                    {banner.description}
                  </div>
                ) : null}
                {banner?.buttonText ? (
                  <div className="mt-20">
                    <Button
                      type="primary"
                      onClick={() => {
                        openPage({ ...banner.url })
                      }}>
                      <div className="flex items-center gap-24 font-miSansDemiBold450 text-[16px] leading-[20px]">
                        {banner.buttonText}
                        <IconArrow color="#fff" rotate={-90} size={20} />
                      </div>
                    </Button>
                  </div>
                ) : null}
              </div>
            </>
          )}
        </div>
      ) : null}
      {/* 商品列表 */}
      {products.length > 0 ? (
        <CardGrid columns={hasCategories ? 3 : 4}>
          {currentProducts.map((product) => (
            <ProductCard key={product.sku} product={product} currentTime={currentTime} />
          ))}
        </CardGrid>
      ) : (
        <ProductEmpty isSearch={isSearchResult} searchTerm={searchTerm} />
      )}

      {/* 加载更多按钮 */}
      {products.length > 0 && hasMore && (
        <div className="mt-base-48 flex justify-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="flex min-w-[200px] items-center justify-center gap-2 rounded-full bg-gray-base px-[28px] py-base-12 font-miSansMedium380 text-[16px] leading-[20px] hover:bg-[#f8f8f9] disabled:cursor-not-allowed disabled:opacity-70">
            {isLoadingMore ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
                {getI18nString('loading')}
              </>
            ) : (
              <>
                {`${getI18nString('load_more_count', {
                  key: total ? ` ${total - currentProducts.length}` : 0,
                })}`}
                <IconArrow size={20} />
              </>
            )}
          </button>
        </div>
      )}

      {/* 显示骨架屏 - 在现有商品下方懒加载显示 */}
      {isLoadingMore && (
        <div className="mt-base-24 transition-all duration-300 ease-in-out">
          <ProductCardSkeleton
            length={Math.min(currentPageSize, 8)} // 限制骨架屏数量，避免页面过长
            containerStyle={{ minHeight: 450 }}
            perRow={hasCategories ? 3 : 4}
          />
        </div>
      )}
    </div>
  )
}
