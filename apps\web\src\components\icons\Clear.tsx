export default function Clear({
  fill = 'black',
  bg = '#F3F3F4',
  width = 32,
  height = 32,
}: {
  fill?: string
  bg?: string
  width?: number
  height?: number
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_b_24931_96659)">
        <path
          d="M0 16C0 7.16344 7.16344 0 16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16Z"
          fill={bg}
        />
        <path
          d="M10.4065 9.14917L9.14941 10.4062L14.1777 15.4346L15.4348 15.4346L15.4348 14.1775L10.4065 9.14917Z"
          fill={fill}
        />
        <path
          d="M9.14917 21.72L10.4062 22.9771L15.4346 17.9487L15.4346 16.6917L14.1775 16.6917L9.14917 21.72Z"
          fill={fill}
        />
        <path
          d="M22.9768 21.72L21.7197 22.9771L16.6914 17.9487L16.6914 16.6917L17.9485 16.6917L22.9768 21.72Z"
          fill={fill}
        />
        <path
          d="M22.9768 10.4062L21.7197 9.14917L16.6914 14.1775L16.6914 15.4346L17.9485 15.4346L22.9768 10.4062Z"
          fill={fill}
        />
      </g>
      <defs>
        <filter
          id="filter0_b_24931_96659"
          x="-13.3333"
          y="-13.3333"
          width="58.6667"
          height="58.6667"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="6.66667" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_24931_96659"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_24931_96659"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
